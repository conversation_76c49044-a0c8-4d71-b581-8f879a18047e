<script>
  import UserNav from '$components/ui/UserNav.svelte';
  import Logo from '$components/ui/Logo.svelte';
  import FeedbackDropdown from '$components/ui/FeedbackDropdown.svelte';
  import NotificationDropdown from '$components/ui/NotificationDropdown.svelte';
  import { Button } from '$lib/components/ui/button';
  import { Menu, X } from 'lucide-svelte';
  import { onMount } from 'svelte';

  const { currentUser = null } = $props();

  let isMenuOpen = $state(false);
  let isLoggedIn = $derived(currentUser !== null);
  let isScrolled = $state(false);

  function toggleMenu() {
    isMenuOpen = !isMenuOpen;
  }

  onMount(() => {
    const handleScroll = () => {
      isScrolled = window.scrollY > 10;
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  });
</script>

<!-- Modern Header with Glass Morphism -->
<header
  class="fixed left-0 right-0 top-0 z-50 transition-all duration-300 {isScrolled
    ? 'glass-card shadow-medium'
    : 'bg-background/80 backdrop-blur-sm'}">
  <div class="container mx-auto px-4">
    <div class="flex h-16 items-center justify-between">
      <!-- Logo Section -->
      <div class="animate-fade-in-left flex items-center gap-3">
        <Logo class="hover-scale h-8 w-8" />
        <a
          href="/"
          class="font-inter text-foreground hover:text-foreground/80 hover-glow text-xl font-bold transition-all duration-200">
          Hirli
        </a>
      </div>

      <!-- Center space for clean design -->
      <div class="flex-1"></div>

      <!-- Right Section -->
      <div class="animate-fade-in-right flex items-center gap-4 delay-300">
        {#if isLoggedIn}
          <!-- Authenticated User Actions -->
          <div class="hidden items-center gap-3 md:flex">
            <FeedbackDropdown />
            <NotificationDropdown />
          </div>
          <UserNav userData={currentUser} />
        {:else}
          <!-- Guest Actions -->
          <div class="hidden items-center gap-3 md:flex">
            <Button variant="ghost" href="/auth/login" class="hover-lift">Sign In</Button>
            <Button
              href="/auth/register"
              class="gradient-primary hover-lift shadow-colored text-white">
              Get Started
            </Button>
          </div>
        {/if}

        <!-- Mobile Menu Toggle -->
        <Button variant="ghost" size="sm" class="hover-scale md:hidden" onclick={toggleMenu}>
          {#if isMenuOpen}
            <X class="h-5 w-5" />
          {:else}
            <Menu class="h-5 w-5" />
          {/if}
        </Button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    {#if isMenuOpen}
      <div class="border-border/50 animate-fade-in-down border-t py-4 md:hidden">
        <div class="flex flex-col space-y-3">
          {#if !isLoggedIn}
            <div class="border-border/50 flex flex-col gap-2 border-t pt-4">
              <Button variant="ghost" href="/auth/login" class="justify-start">Sign In</Button>
              <Button href="/auth/register" class="gradient-primary justify-start text-white">
                Get Started
              </Button>
            </div>
          {:else}
            <div class="border-border/50 flex flex-col gap-2 border-t pt-4">
              <FeedbackDropdown />
              <NotificationDropdown />
            </div>
          {/if}
        </div>
      </div>
    {/if}
  </div>
</header>

<!-- Spacer to prevent content from hiding behind fixed header -->
<div class="h-16"></div>
