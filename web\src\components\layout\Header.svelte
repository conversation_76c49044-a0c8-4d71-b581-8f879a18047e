<script>
  import UserNav from '$components/ui/UserNav.svelte';
  import Logo from '$components/ui/Logo.svelte';
  import MainNav from '$components/layout/MainNav.svelte';
  import FeedbackDropdown from '$components/ui/FeedbackDropdown.svelte';
  import NotificationDropdown from '$components/ui/NotificationDropdown.svelte';
  import { Button } from '$lib/components/ui/button';
  import { Menu, X } from 'lucide-svelte';
  import { onMount } from 'svelte';

  const { currentUser = null } = $props();

  let isMenuOpen = $state(false);
  let isLoggedIn = $derived(currentUser !== null);
  let isScrolled = $state(false);

  function toggleMenu() {
    isMenuOpen = !isMenuOpen;
  }

  onMount(() => {
    const handleScroll = () => {
      isScrolled = window.scrollY > 10;
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  });
</script>

<!-- Modern Header with Glass Morphism -->
<header
  class="fixed left-0 right-0 top-0 z-50 transition-all duration-300 {isScrolled
    ? 'glass-card shadow-medium'
    : 'bg-background/80 backdrop-blur-sm'}"
>
  <div class="container mx-auto px-4">
    <div class="flex h-16 items-center justify-between">
      <!-- Logo Section -->
      <div class="flex items-center gap-3 animate-fade-in-left">
        <Logo class="h-8 w-8 hover-scale" />
        <a
          href="/"
          class="font-inter text-foreground hover:text-foreground/80 text-xl font-bold transition-all duration-200 hover-glow">
          Hirli
        </a>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center justify-center flex-1 animate-fade-in-down delay-200">
        <MainNav {isLoggedIn} className="modern-nav" />
      </div>

      <!-- Right Section -->
      <div class="flex items-center gap-4 animate-fade-in-right delay-300">
        {#if isLoggedIn}
          <!-- Authenticated User Actions -->
          <div class="hidden md:flex items-center gap-3">
            <FeedbackDropdown />
            <NotificationDropdown />
          </div>
          <UserNav {currentUser} />
        {:else}
          <!-- Guest Actions -->
          <div class="hidden md:flex items-center gap-3">
            <Button
              variant="ghost"
              href="/auth/login"
              class="hover-lift"
            >
              Sign In
            </Button>
            <Button
              href="/auth/register"
              class="gradient-primary text-white hover-lift shadow-colored"
            >
              Get Started
            </Button>
          </div>
        {/if}

        <!-- Mobile Menu Toggle -->
        <Button
          variant="ghost"
          size="sm"
          class="md:hidden hover-scale"
          onclick={toggleMenu}
        >
          {#if isMenuOpen}
            <X class="h-5 w-5" />
          {:else}
            <Menu class="h-5 w-5" />
          {/if}
        </Button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    {#if isMenuOpen}
      <div class="md:hidden border-t border-border/50 py-4 animate-fade-in-down">
        <div class="flex flex-col space-y-3">
          <MainNav {isLoggedIn} className="mobile-nav flex-col space-y-2" />

          {#if !isLoggedIn}
            <div class="flex flex-col gap-2 pt-4 border-t border-border/50">
              <Button
                variant="ghost"
                href="/auth/login"
                class="justify-start"
              >
                Sign In
              </Button>
              <Button
                href="/auth/register"
                class="gradient-primary text-white justify-start"
              >
                Get Started
              </Button>
            </div>
          {:else}
            <div class="flex flex-col gap-2 pt-4 border-t border-border/50">
              <FeedbackDropdown />
              <NotificationDropdown />
            </div>
          {/if}
        </div>
      </div>
    {/if}
  </div>
</header>

<!-- Spacer to prevent content from hiding behind fixed header -->
<div class="h-16"></div>

      <MainNav {isLoggedIn} />
    </div>
    <!-- Auth Buttons / User Menu -->
    {#if isLoggedIn}
      <div class="ml-auto mr-6 flex items-center gap-2 space-x-4">
        <div class="mr-4 flex flex-row gap-6">
          <FeedbackDropdown />
          <NotificationDropdown />
        </div>

        <UserNav userData={currentUser} />
      </div>
    {:else}
      <div class="text-md hidden items-center space-x-4 md:flex">
        <a href="/contact" class="text-foreground hover:text-primary px-4 py-2 transition"
          >Contact</a>
        <a href="/auth/sign-in" class="text-foreground hover:text-primary px-4 py-2 transition"
          >Sign In</a>
        <a
          href="/auth/sign-up"
          class="bg-background/70 text-primary-foreground hover:bg-primary/90 px-8 py-6 transition"
          >Get Started</a>
      </div>
    {/if}

    <!-- Mobile Menu Toggle -->
    <button
      onclick={toggleMenu}
      class="text-foreground focus:outline-none md:hidden"
      aria-label="Toggle Menu">
      {#if isMenuOpen}
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12" />
        </svg>
      {:else}
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      {/if}
    </button>
  </div>
</nav>
