<script>
  import { onMount } from 'svelte';
  import { But<PERSON> } from '$lib/components/ui/button';
  import Logo from '$components/ui/Logo.svelte';
  import AnimatedContainer from '$lib/components/ui/AnimatedContainer.svelte';

  let visible = false;

  onMount(() => {
    visible = true;
  });
</script>

<section
  class="from-background via-background to-muted/10 relative overflow-hidden border-b bg-gradient-to-br py-20 md:py-40">
  <!-- Animated background elements -->
  <div class="absolute inset-0 z-0 overflow-hidden">
    {#if visible}
      <div class="animate-float absolute right-20 top-20 opacity-5 delay-1000">
        <Logo class="h-64 w-64" />
      </div>
      <div class="animate-float absolute bottom-20 left-40 opacity-5 delay-500">
        <Logo class="h-48 w-48" />
      </div>
      <div class="animate-float absolute right-1/4 top-1/2 opacity-5">
        <Logo class="h-56 w-56" />
      </div>
    {/if}
  </div>

  <!-- Gradient overlay -->
  <div class="from-primary/5 to-accent/5 absolute inset-0 bg-gradient-to-r via-transparent"></div>

  <div class="container relative z-10 mx-auto px-4">
    <div class="grid grid-cols-1 items-center gap-12 md:grid-cols-2">
      <AnimatedContainer animation="fade-in-up" delay={0}>
        <h1 class="leading-tighter mb-8 w-[90%] text-4xl font-light md:text-5xl lg:text-[80px]">
          Streamline your <span class="gradient-text">Job Search</span>, get more interviews.
        </h1>
      </AnimatedContainer>

      <AnimatedContainer animation="fade-in-up" delay={200}>
        <p class="text-muted-foreground mb-12 text-lg md:text-2xl">
          Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings,
          auto-fills applications, and tracks your progress.
        </p>
      </AnimatedContainer>

      <AnimatedContainer animation="fade-in-up" delay={400}>
        <div class="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
          <Button
            class="gradient-primary text-white hover-lift shadow-colored rounded-lg border border-transparent p-8 text-lg font-medium">
            Get Started Free
          </Button>
          <Button
            variant="outline"
            class="hover-lift group flex items-center rounded-lg border p-8 text-lg font-medium">
            How It Works
            <svg class="ml-4 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </Button>
        </div>
      </AnimatedContainer>

      <AnimatedContainer animation="fade-in-up" delay={600}>
        <div class="text-muted-foreground mt-8 flex items-center text-sm">
          <div class="mr-3 flex -space-x-2">
            <img
              src="https://randomuser.me/api/portraits/women/79.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
            <img
              src="https://randomuser.me/api/portraits/men/32.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
            <img
              src="https://randomuser.me/api/portraits/women/44.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
          </div>
          <span
            >Join <span class="font-semibold">10,000+</span> job seekers finding work faster</span>
        </div>
      {/if}
    </div>

    <!-- Image content -->
    <div class="relative hidden md:block">
      <div class="bg-muted/20 flex h-[400px] items-center justify-center rounded-lg p-8">
        <div class="text-center">
          <div
            class="bg-primary/20 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full">
            <i class="text-primary fa fa-search text-2xl"></i>
          </div>
          <h3 class="mb-2 text-xl font-medium">Smart Job Matching</h3>
          <p class="text-muted-foreground">
            Our AI finds the perfect jobs for your skills and experience
          </p>
        </div>
      </div>
    </div>
  </div>
</section>
