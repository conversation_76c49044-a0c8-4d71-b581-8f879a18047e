<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Carousel from '$lib/components/ui/carousel/index.js';
  import AnimatedContainer from '$lib/components/ui/AnimatedContainer.svelte';

  import Autoplay from 'embla-carousel-autoplay';
  import type { AlignmentOptionType } from 'embla-carousel/components/Alignment';

  const companies = [
    {
      name: 'Google',
      logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/google/google-original.svg',
    },
    {
      name: 'Microsoft',
      logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/microsoft/microsoft-original.svg',
    },
    {
      name: 'Amazon',
      logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg',
    },
    {
      name: 'Apple',
      logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/apple/apple-original.svg',
    },
    {
      name: 'Meta',
      logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/facebook/facebook-original.svg',
    },
    {
      name: 'Netflix',
      logo: 'https://images.ctfassets.net/y2ske730sjqp/1aONibCke6niZhgPxuiilC/2c401b05a07288746ddf3bd3943fbc76/BrandAssets_Logos_01-Wordmark.jpg',
    },
    {
      name: 'Uber',
      logo: 'https://d1a3f4spazzrp4.cloudfront.net/uber-com/1.3.8/d1a3f4spazzrp4.cloudfront.net/images/uber-logo.svg',
    },
    {
      name: 'Airbnb',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/6/69/Airbnb_Logo_Bélo.svg',
    },
  ];

  // Create three arrays with different orderings for visual variety
  const companiesRow1 = [...companies];
  const companiesRow2 = [...companies].reverse();
  const companiesRow3 = [...companies].sort(() => Math.random() - 0.5);

  // Create autoplay plugins with different settings for each carousel
  const plugin1 = Autoplay({ delay: 2000, stopOnInteraction: true });
  const plugin2 = Autoplay({ delay: 4000, stopOnInteraction: true });
  const plugin3 = Autoplay({ delay: 2500, stopOnInteraction: true });

  // Set reverse direction for the middle carousel
  const options1 = { align: 'start' as AlignmentOptionType, loop: true };
  const options2 = { align: 'start' as AlignmentOptionType, loop: true };
  const options3 = { align: 'start' as const, loop: true };
</script>

<section
  class="from-muted/30 via-background to-muted/20 relative overflow-hidden bg-gradient-to-br py-20">
  <!-- Background decoration -->
  <div class="absolute inset-0 opacity-5">
    <div class="animate-float absolute right-10 top-10 delay-1000">
      <div class="bg-primary/20 h-20 w-20 rounded-full"></div>
    </div>
    <div class="animate-float absolute bottom-10 left-10 delay-500">
      <div class="bg-accent/20 h-16 w-16 rounded-full"></div>
    </div>
  </div>

  <div class="container relative mx-auto px-4">
    <AnimatedContainer animation="fade-in-up" triggerOnScroll={true}>
      <div class="mb-16 text-center">
        <h2 class="mb-4 text-3xl font-bold md:text-4xl">
          Trusted by professionals at <span class="gradient-text">top companies</span>
        </h2>
        <p class="text-muted-foreground mx-auto max-w-2xl text-lg">
          Join thousands of professionals who've found their dream jobs through our platform
        </p>
      </div>
    </AnimatedContainer>

    <!-- First row - left to right -->
    <AnimatedContainer animation="fade-in-left" delay={200} triggerOnScroll={true}>
      <div class="mb-8">
        <Carousel.Root plugins={[plugin1]} opts={options1} class="w-full">
          <Carousel.Content>
            {#each companiesRow1 as company, i (i)}
              <Carousel.Item class="md:basis-1/4 lg:basis-1/6">
                <div class="p-2">
                  <div class="floating-card hover-lift group p-6 transition-all duration-300">
                    <div class="flex h-16 items-center justify-center">
                      <img
                        src={company.logo}
                        alt="{company.name} logo"
                        class="max-h-10 max-w-[100px] opacity-60 transition-all duration-300 group-hover:scale-110 group-hover:opacity-100 dark:opacity-50 dark:group-hover:opacity-90"
                        on:error={(e) =>
                          ((e.currentTarget as HTMLImageElement).src =
                            `https://placehold.co/200x60/6366f1/ffffff?text=${company.name}`)} />
                    </div>
                  </div>
                </div>
              </Carousel.Item>
            {/each}
          </Carousel.Content>
        </Carousel.Root>
      </div>
    </AnimatedContainer>

    <!-- Second row - right to left -->
    <div class="mb-8">
      <Carousel.Root
        plugins={[plugin2]}
        opts={options2}
        class="w-full"
        on:mouseenter={plugin2.stop}
        on:mouseleave={() => plugin2.play()}>
        <Carousel.Content>
          {#each companiesRow2 as company, i (i)}
            <Carousel.Item class="md:basis-1/4 lg:basis-1/6">
              <div class="p-1">
                <Card.Root
                  class="hover:border-primary/10 transition-all duration-300 hover:shadow-md">
                  <Card.Content class="flex h-24 items-center justify-center p-6">
                    <img
                      src={company.logo}
                      alt="{company.name} logo"
                      class="max-h-12 max-w-[120px] opacity-70 transition-all duration-300 hover:scale-105 hover:opacity-100 dark:opacity-60 dark:hover:opacity-90"
                      on:error={(e) =>
                        ((e.currentTarget as HTMLImageElement).src =
                          `https://placehold.co/200x80?text=${company.name}`)} />
                  </Card.Content>
                </Card.Root>
              </div>
            </Carousel.Item>
          {/each}
        </Carousel.Content>
      </Carousel.Root>
    </div>

    <!-- Third row - left to right -->
    <div class="mb-8">
      <Carousel.Root
        plugins={[plugin3]}
        opts={options3}
        class="w-full"
        on:mouseenter={plugin3.stop}
        on:mouseleave={() => plugin3.play()}>
        <Carousel.Content>
          {#each companiesRow3 as company, i (i)}
            <Carousel.Item class="md:basis-1/4 lg:basis-1/6">
              <div class="p-1">
                <Card.Root
                  class="hover:border-primary/10 transition-all duration-300 hover:shadow-md">
                  <Card.Content class="flex h-24 items-center justify-center p-6">
                    <img
                      src={company.logo}
                      alt="{company.name} logo"
                      class="max-h-12 max-w-[120px] opacity-70 transition-all duration-300 hover:scale-105 hover:opacity-100 dark:opacity-60 dark:hover:opacity-90"
                      on:error={(e) =>
                        ((e.currentTarget as HTMLImageElement).src =
                          `https://placehold.co/200x80?text=${company.name}`)} />
                  </Card.Content>
                </Card.Root>
              </div>
            </Carousel.Item>
          {/each}
        </Carousel.Content>
      </Carousel.Root>
    </div>

    <p class="text-md text-muted-foreground mt-8 text-center">
      Join thousands of professionals who've found their dream jobs through our platform
    </p>
  </div>
</section>
