<script lang="ts">
  import Logo from '$components/ui/Logo.svelte';
  import { onMount } from 'svelte';
  import { graphqlRequest, COLLECTIONS_QUERY } from '$lib/graphql/client';
  import type { Collection } from '$lib/graphql/types';
  import { ThemeToggle } from '$components/theme';
  import { store } from '$lib/stores/store';

  // Accept any data type since we're only using it for job collections if available
  export let data: any = {};

  const currentYear = new Date().getFullYear();
  let jobCollections: Collection[] = [];
  let topCollections: Collection[] = [];

  // Navigation links organized in arrays
  const productLinks = [
    { href: '/auto-apply', label: 'Auto Apply' },
    { href: '/job-tracker', label: 'Job Tracker' },
    { href: '/resume-builder', label: 'Resume Builder' },
    { href: '/co-pilot', label: 'AI Co-Pilot' },
    { href: '/pricing', label: 'Pricing' },
  ];

  const fallbackCategoryLinks = [
    { href: '/tech-jobs', label: 'Tech Jobs' },
    { href: '/remote-jobs', label: 'Remote Jobs' },
    { href: '/entry-level', label: 'Entry Level' },
    { href: '/jobs', label: 'Browse All Jobs' },
  ];

  const supportLinks = [
    { href: '/help', label: 'Help Center' },
    { href: '/system-status', label: 'System Status' },
    { href: 'https://autoapply.featurebase.app/', label: 'Submit Feedback' },
    { href: 'https://autoapply.featurebase.app/roadmap', label: 'Roadmap' },
    { href: '/contact', label: 'Contact Us' },
  ];

  const resourceLinks = [
    { href: '/resources', label: 'Free Tools' },
    { href: '/resources/resume-templates', label: 'Resume Templates' },
    { href: '/resources/cover-letters', label: 'Cover Letter Templates' },
    { href: '/resources/ats-optimization/checker', label: 'ATS Resume Checker' },
    { href: '/resources/interview-prep/question-database', label: 'Interview Questions' },
    { href: '/resources/salary-tools', label: 'Salary Tools' },
  ];

  const companyLinks = [
    { href: '/about', label: 'About' },
    { href: '/blog', label: 'Blog' },
    { href: '/press', label: 'Press & Media' },
  ];

  const socialLinks = [
    { href: 'https://tiktok.com/hirli', label: 'Tiktok' },
    { href: 'https://linkedin.com/company/hirli', label: 'LinkedIn' },
    { href: 'https://x.com/hirliapp', label: 'X' },
    { href: 'https://instagram.com/hirliapp', label: 'Instagram' },
  ];

  const legalLinks = [
    { href: '/legal', label: 'Legal' },
    { href: '/legal/terms', label: 'Terms' },
    { href: '/legal/privacy-policy', label: 'Privacy' },
    { href: '/legal/cookie-policy', label: 'Cookies' },
    { href: '/legal/accessibility', label: 'Accessibility' },
    { href: '/sitemap.xml', label: 'Sitemap' },
  ];

  // Load job collections
  async function loadJobCollections() {
    try {
      // Try to use collections from data prop first
      if (data?.jobCollections && Array.isArray(data.jobCollections)) {
        jobCollections = data.jobCollections;
      } else {
        // Fallback to GraphQL request
        const result = await graphqlRequest<{ collections: Collection[] }>(COLLECTIONS_QUERY);
        if (result.errors) {
          throw new Error(result.errors[0].message);
        }
        jobCollections = result.data?.collections || [];
      }

      // Get top 10 collections (alphabetically sorted)
      topCollections = jobCollections.slice(0, 10);
    } catch (error) {
      console.error('Error loading job collections for footer:', error);
    }
  }

  // Initialize on mount
  onMount(() => {
    loadJobCollections();
  });
</script>

<footer
  class="border-border/50 from-background via-background to-muted/20 relative overflow-hidden border-t bg-gradient-to-br">
  <!-- Background decoration -->
  <div class="absolute inset-0 opacity-5">
    <div class="animate-float absolute right-20 top-20 delay-1000">
      <Logo class="h-32 w-32" />
    </div>
    <div class="animate-float absolute bottom-20 left-20 delay-500">
      <Logo class="h-24 w-24" />
    </div>
  </div>

  <div class="container relative mx-auto px-8 py-16">
    <div class="grid grid-cols-1 gap-12 lg:grid-cols-2">
      <!-- Logo and Tagline -->
      <div class="animate-fade-in-up">
        <a href="/" class="hover-scale inline-block" aria-label="Hirli Home">
          <Logo class="h-12 w-12" />
        </a>
        <h2 class="mt-6 text-3xl font-light leading-tight">
          Automate your <br />
          <span class="gradient-text">job search</span>.
        </h2>
        <p class="text-muted-foreground mt-4 max-w-md text-lg">
          Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings
          and tracks your progress.
        </p>
      </div>

      <!-- Main Links Section - Grid Layout -->
      <div class="animate-fade-in-up delay-200">
        <div class="grid grid-cols-2 gap-6 md:grid-cols-4">
          <!-- Products Column -->
          <div class="floating-card hover-lift p-6">
            <h5 class="text-foreground mb-4 text-sm font-semibold uppercase tracking-wider">
              Products
            </h5>
            <ul class="space-y-3">
              {#each productLinks as link}
                <li>
                  <a
                    href={link.href}
                    class="text-muted-foreground hover:text-foreground interactive text-sm transition-colors">
                    {link.label}
                  </a>
                </li>
              {/each}
            </ul>
          </div>

          <!-- Job Categories Column -->
          <div>
            <h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">
              Categories
            </h5>
            <ul class="space-y-1.5">
              {#if topCollections.length > 0}
                {#each topCollections as collection}
                  <li>
                    <a
                      href="/jobs?collection={collection.slug}"
                      class="text-muted-foreground hover:text-muted-foreground/80 text-sm">
                      {collection.name}
                    </a>
                  </li>
                {/each}
              {:else}
                {#each fallbackCategoryLinks.slice(0, 3) as link}
                  <li>
                    <a
                      href={link.href}
                      class="text-muted-foreground hover:text-muted-foreground/80 text-sm">
                      {link.label}
                    </a>
                  </li>
                {/each}
              {/if}
              <li>
                <a
                  href="/jobs"
                  class="text-muted-foreground hover:text-muted-foreground/80 text-sm">
                  Browse All Jobs
                </a>
              </li>
            </ul>
          </div>

          <!-- Support Column (with Resources merged in) -->
          <div>
            <h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">
              Support
            </h5>
            <ul class="mb-6 space-y-1.5">
              {#each supportLinks as link}
                <li>
                  <a
                    href={link.href}
                    class="text-muted-foreground hover:text-muted-foreground/80 text-sm">
                    {link.label}
                  </a>
                </li>
              {/each}
            </ul>

            <h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">
              Resources
            </h5>
            <ul class="space-y-1.5">
              {#each resourceLinks as link}
                <li>
                  <a
                    href={link.href}
                    class="text-muted-foreground hover:text-muted-foreground/80 text-sm">
                    {link.label}
                  </a>
                </li>
              {/each}
            </ul>
          </div>

          <!-- New Column with Company and Follow sections -->
          <div class="space-y-8">
            <!-- Company Section -->
            <div>
              <h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">
                Company
              </h5>
              <ul class="space-y-1.5">
                {#each companyLinks as link}
                  <li>
                    <a
                      href={link.href}
                      class="text-muted-foreground hover:text-muted-foreground/80 text-sm">
                      {link.label}
                    </a>
                  </li>
                {/each}
              </ul>
            </div>

            <!-- Follow Section (Social Links) -->
            <div class="space-y-8">
              <h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">
                Follow
              </h5>
              <ul class="space-y-1.5">
                {#each socialLinks as link}
                  <li>
                    <a
                      href={link.href}
                      class="text-muted-foreground hover:text-muted-foreground/80 text-sm"
                      aria-label={link.label}>
                      {link.label}
                    </a>
                  </li>
                {/each}
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="mx-8 py-6">
        <div
          class="border-border flex w-full flex-col items-center justify-between border-t pt-6 md:flex-row">
          <div class="mb-4 md:mb-0">
            <ThemeToggle variant="outline" size="icon" />
          </div>
          <div class="text-muted-foreground flex flex-wrap justify-center gap-4 text-sm">
            {#each legalLinks as link}
              <a href={link.href} class="hover:text-foreground">{link.label}</a>
            {/each}
            <p class="ml-4">© {currentYear} Hirli, Inc.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>
